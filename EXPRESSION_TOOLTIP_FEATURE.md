# Expression Tooltip Feature

## Overview
Added a tooltip at the bottom of expression fields that shows the real-time result of the expression evaluation. This feature helps users understand what their expressions will resolve to before saving the form.

## Features Implemented

### 1. Enhanced Expression Evaluation (`src/utils/expressionUtils.ts`)
- **Expression Evaluation Engine**: Evaluates expressions against available node data
- **Result Formatting**: Formats evaluation results for display
- **Error Handling**: Provides detailed error messages for invalid expressions
- **Preview Generation**: Creates short previews of complex results

### 2. Enhanced DragDropContext (`src/contexts/DragDropContext.tsx`)
- **Available Nodes Storage**: Stores connected nodes data for expression evaluation
- **Global Access**: Makes node data available throughout the component tree
- **Type Safety**: Provides TypeScript interfaces for node data

### 3. Enhanced ExpressionInput (`src/components/ExpressionInput.tsx`)
- **Real-time Evaluation**: Evaluates expressions as user types
- **Tooltip Display**: Shows evaluation results in a styled tooltip
- **Error Display**: Shows validation and evaluation errors
- **Expandable Results**: Allows viewing full JSON results for complex objects

### 4. Updated NodeModal (`src/pages/flow-editor/NodeModal.tsx`)
- **Context Integration**: Provides available nodes data to expression inputs
- **Automatic Updates**: Updates available nodes when connected nodes change

## How It Works

### Expression Evaluation Process
1. **User Types Expression**: As user types in expression mode, the input validates syntax
2. **Real-time Evaluation**: If syntax is valid, evaluates against available node data
3. **Result Display**: Shows the result in a tooltip below the input field
4. **Error Handling**: Displays helpful error messages for invalid expressions or missing data

### Expression Format
Expressions use the format: `NodeName.path.to.data`

Examples:
- `Switch.name` - Access the 'name' field from Switch node
- `API_Call.response.data[0].id` - Access nested array data
- `User_Input.form.email` - Access form field data

### Tooltip Features
- **Success Indicator**: Green checkmark for successful evaluations
- **Error Indicator**: Red X for failed evaluations
- **Type Display**: Shows the data type of the result (string, number, object, etc.)
- **Preview**: Shows a truncated preview of the result
- **Expandable**: Click "Show full result" to see complete JSON output
- **Scrollable**: Long results are scrollable within the tooltip

## Visual Design

### Success State
```
✓ Result: string
"Hello World"
[Show full result]
```

### Error State
```
✗ Error:
Node "NonExistentNode" not found. Available nodes: Switch, API_Call
```

### Complex Object Result
```
✓ Result: object
{"name": "John", "age": 30, ...}
[Show full result]
```

## Technical Implementation

### Expression Evaluation Function
```typescript
evaluateExpression(expression: string, availableNodes: NodeDataForEvaluation[]): ExpressionEvaluationResult
```

### Result Formatting
```typescript
formatEvaluationResult(result: ExpressionEvaluationResult): string
getEvaluationPreview(result: ExpressionEvaluationResult, maxLength: number): string
```

### Context Integration
- Available nodes are automatically updated when modal opens
- Expression inputs access nodes through React Context
- Real-time updates when node connections change

## Error Handling

### Common Error Types
1. **Syntax Errors**: Invalid expression format
2. **Missing Nodes**: Referenced node doesn't exist
3. **Missing Data**: Node has no execution data
4. **Invalid Paths**: Path doesn't exist in node output
5. **Evaluation Errors**: Runtime errors during evaluation

### Error Messages
- **Clear and Helpful**: Explains what went wrong and how to fix it
- **Context Aware**: Shows available nodes when referencing missing ones
- **Actionable**: Provides specific guidance for resolution

## Usage Examples

### Basic Field Access
```
Expression: Switch.status
Result: "active"
Type: string
```

### Nested Object Access
```
Expression: API_Call.response.user.profile.name
Result: "John Doe"
Type: string
```

### Array Access
```
Expression: DataList.items[0].id
Result: 12345
Type: number
```

### Complex Object
```
Expression: UserData.profile
Result: {"name": "John", "email": "<EMAIL>", "age": 30}
Type: object
[Show full result] - expands to show formatted JSON
```

## Benefits

### For Users
- **Immediate Feedback**: See expression results without running the workflow
- **Error Prevention**: Catch expression errors before saving
- **Learning Tool**: Understand expression syntax through examples
- **Debugging Aid**: Quickly identify issues with expressions

### For Developers
- **Type Safety**: Full TypeScript support for expression evaluation
- **Extensible**: Easy to add new expression features
- **Maintainable**: Clean separation of concerns
- **Testable**: Isolated evaluation logic

## Future Enhancements

### Planned Features
- **Auto-completion**: Suggest available fields while typing
- **Expression Builder**: Visual interface for building expressions
- **Function Support**: Built-in functions for data manipulation
- **Validation Rules**: Custom validation for specific field types
- **Performance Optimization**: Debounced evaluation for large datasets

### Advanced Features
- **Expression History**: Remember previously used expressions
- **Template Expressions**: Pre-built expression templates
- **Conditional Logic**: Support for if/else expressions
- **Data Transformation**: Built-in functions for formatting data
