# Drag and Drop Implementation

## Overview
This implementation adds comprehensive drag-and-drop functionality to your workflow editor, allowing users to drag data from previous nodes into parameter fields as expressions.

## Features Implemented

### 1. Drag-Drop Context (`src/contexts/DragDropContext.tsx`)
- Provides global state management for drag-drop operations
- Tracks drag data, dragging state, and drop targets
- Centralized context for all drag-drop interactions

### 2. Expression Utilities (`src/utils/expressionUtils.ts`)
- Expression parsing and formatting functions
- Data path extraction for nested objects
- Expression validation
- Utility functions for creating expressions from drag data

### 3. Enhanced Expression Input (`src/components/ExpressionInput.tsx`)
- Dual-mode input: Fixed values or Expressions
- Visual mode indicator and toggle button
- Drag-drop target with visual feedback
- Expression validation with error display
- Support for both single-line and multiline inputs

### 4. Draggable Data Items (`src/components/DraggableDataItem.tsx`)
- Makes data items draggable with proper data transfer
- Visual feedback during drag operations
- Type-aware icons and value previews
- Hover effects and drag indicators

### 5. Data Tree Component (`src/components/DataTree.tsx`)
- Hierarchical view of nested data structures
- Expandable/collapsible tree nodes
- Draggable leaf nodes
- Type-aware icons and formatting
- Configurable depth limits

### 6. Updated NodeModal (`src/pages/flow-editor/NodeModal.tsx`)
- Renamed tabs: "Previous Nodes", "Parameters", "Next Nodes"
- Integrated drag-drop context
- Enhanced data visualization with tree view
- Improved layout and user experience

### 7. Enhanced FieldRenderer (`src/components/shared/FieldRenderer.tsx`)
- Integrated ExpressionInput for string and JSON fields
- Maintains compatibility with existing form handling
- Supports both fixed values and expressions

## How to Use

### 1. Open Node Modal
- Double-click any node in the workflow editor
- The modal opens with three tabs: Previous Nodes, Parameters, Next Nodes

### 2. Navigate to Previous Nodes Tab
- View connected input nodes and their data
- Browse data in a hierarchical tree structure
- See data types and values for each field

### 3. Drag Data to Parameters
- Switch to the Parameters tab
- Find the parameter field you want to populate
- Drag any data item from Previous Nodes into the parameter field
- The field automatically switches to expression mode
- The expression is created using the format: `NodeName.dataPath`

### 4. Toggle Between Fixed and Expression Modes
- Click the toggle button (Type/Code icon) next to any parameter field
- Fixed mode: Enter literal values
- Expression mode: Enter expressions or drag data

### 5. Expression Validation
- Invalid expressions show error messages
- Visual feedback for valid/invalid expressions
- Real-time validation as you type

## Expression Format

Expressions use the format: `{{ NodeName.dataPath }}`

Examples:
- `{{ Switch.name }}` - Access the 'name' field from Switch node
- `{{ API_Call.response.data[0].id }}` - Access nested array data
- `{{ User_Input.form.email }}` - Access form field data

## Visual Feedback

### Drag Operations
- Draggable items show grip icon on hover
- Drop targets highlight when dragging compatible data
- Visual indicators show drop zones

### Expression Mode
- Blue background indicates expression mode
- Expression badge shows current mode
- Validation errors display with icons

### Data Types
- Different icons for strings, numbers, booleans, objects, arrays
- Type labels show data types
- Value previews for primitive types

## Technical Details

### State Management
- Uses React Context for global drag-drop state
- Form integration with react-hook-form
- Proper event handling for drag-drop operations

### Performance
- Tree view with configurable depth limits
- Efficient re-rendering with proper React patterns
- Minimal re-renders during drag operations

### Accessibility
- Keyboard navigation support
- Screen reader friendly
- Proper ARIA labels and roles

## Browser Compatibility
- Modern browsers with HTML5 drag-drop support
- Tested on Chrome, Firefox, Safari, Edge
- Mobile touch support for drag operations

## Future Enhancements
- Auto-completion for expression syntax
- Expression builder UI
- Data type validation for expressions
- Expression testing/preview functionality
- Undo/redo for expression changes
