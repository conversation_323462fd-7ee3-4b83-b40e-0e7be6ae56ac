import React from 'react';
import ExpressionInput from './ExpressionInput';
import { DragDropProvider, useDragDrop, type NodeDataForEvaluation } from '../contexts/DragDropContext';

// Inner component that uses the context
const ExpressionInputTestContent: React.FC = () => {
  const [value, setValue] = React.useState('');
  const { setAvailableNodes } = useDragDrop();

  // Mock available nodes for testing
  const mockNodes: NodeDataForEvaluation[] = [
    {
      id: 'test-node-1',
      label: 'TestNode',
      nodeType: {
        display_name: 'Test Node'
      },
      executionData: {
        output: {
          name: 'Test Output',
          status: 'success',
          data: {
            id: 123,
            message: 'Hello World',
            nested: {
              value: 42,
              array: [1, 2, 3, 'test']
            }
          }
        }
      }
    },
    {
      id: 'test-node-2',
      label: 'AnotherNode',
      nodeType: {
        display_name: 'Another Node'
      },
      executionData: {
        output: {
          result: 'Another result',
          count: 5
        }
      }
    }
  ];

  // Set the mock nodes in the context
  React.useEffect(() => {
    setAvailableNodes(mockNodes);
  }, [setAvailableNodes]);

  return (
    <div className="p-8 max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Expression Input Test</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Test Expression Input
            </label>
            <ExpressionInput
              name="test-expression"
              value={value}
              onChange={setValue}
              placeholder="Try: {TestNode.name} or {TestNode.data.id + 1}"
            />
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Instructions:</h3>
            <ol className="text-sm text-gray-600 space-y-1">
              <li>1. Click the toggle button to switch to expression mode</li>
              <li>2. Try typing: <code className="bg-gray-200 px-1 rounded">{`{TestNode.name}`}</code></li>
              <li>3. Try typing: <code className="bg-gray-200 px-1 rounded">{`{TestNode.data.id + 1}`}</code></li>
              <li>4. Try typing: <code className="bg-gray-200 px-1 rounded">{`{AnotherNode.count * 2}`}</code></li>
              <li>5. Try typing: <code className="bg-gray-200 px-1 rounded">{`{TestNode.data.id + AnotherNode.count}`}</code></li>
              <li>6. Try typing: <code className="bg-gray-200 px-1 rounded">{`{InvalidNode.field}`}</code> (should show error)</li>
            </ol>
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-medium text-blue-700 mb-2">Available Mock Nodes:</h3>
            <pre className="text-xs text-blue-600 overflow-x-auto">
              {JSON.stringify(mockNodes, null, 2)}
            </pre>
          </div>
          
          <div className="mt-6 p-4 bg-green-50 rounded-lg">
            <h3 className="text-sm font-medium text-green-700 mb-2">Current Value:</h3>
            <code className="text-sm text-green-600">{value || '(empty)'}</code>
          </div>
        </div>
      </div>
  );
};

// Main test component with provider
const ExpressionInputTest: React.FC = () => {
  return (
    <DragDropProvider>
      <ExpressionInputTestContent />
    </DragDropProvider>
  );
};

export default ExpressionInputTest;
