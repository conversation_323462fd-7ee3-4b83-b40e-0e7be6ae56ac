import React from 'react';
import { useDragDrop, type DragData } from '../contexts/DragDropContext';
import * as Icons from 'lucide-react';

interface DraggableDataItemProps {
  nodeId: string;
  nodeName: string;
  dataPath: string;
  value: unknown;
  displayName: string;
  icon?: React.ComponentType<{ size?: number; className?: string }>;
  className?: string;
}

const DraggableDataItem: React.FC<DraggableDataItemProps> = ({
  nodeId,
  nodeName,
  dataPath,
  value,
  displayName,
  icon: Icon = Icons.Database,
  className = '',
}) => {
  const { setDragData, setIsDragging } = useDragDrop();

  const handleDragStart = (e: React.DragEvent) => {
    const dragData: DragData = {
      type: 'node-data',
      nodeId,
      nodeName,
      dataPath,
      value,
      displayName,
    };

    setDragData(dragData);
    setIsDragging(true);

    // Set drag effect
    e.dataTransfer.effectAllowed = 'copy';
    e.dataTransfer.setData('application/json', JSON.stringify(dragData));
  };

  const handleDragEnd = () => {
    setDragData(null);
    setIsDragging(false);
  };

  const getValuePreview = (val: unknown): string => {
    if (val === null) return 'null';
    if (val === undefined) return 'undefined';
    if (typeof val === 'string') return `"${val}"`;
    if (typeof val === 'number' || typeof val === 'boolean') return String(val);
    if (typeof val === 'object') {
      if (Array.isArray(val)) return `Array(${val.length})`;
      return 'Object';
    }
    return String(val);
  };

  const getValueTypeIcon = (val: unknown) => {
    if (val === null || val === undefined) return Icons.Minus;
    if (typeof val === 'string') return Icons.Type;
    if (typeof val === 'number') return Icons.Hash;
    if (typeof val === 'boolean') return Icons.ToggleLeft;
    if (Array.isArray(val)) return Icons.List;
    if (typeof val === 'object') return Icons.Braces;
    return Icons.HelpCircle;
  };

  const ValueIcon = getValueTypeIcon(value);
  const valuePreview = getValuePreview(value);

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className={`
        flex items-center space-x-3 p-2 rounded-md border border-gray-200 
        bg-white hover:bg-gray-50 hover:border-gray-300 cursor-grab 
        active:cursor-grabbing transition-colors group
        ${className}
      `}
      title={`Drag to use: ${displayName} = ${valuePreview}`}
    >
      {/* Data type icon */}
      <div className="flex-shrink-0">
        <ValueIcon size={14} className="text-gray-500 group-hover:text-gray-700" />
      </div>

      {/* Data path and value */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-900 truncate">
            {displayName}
          </span>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {typeof value}
          </span>
        </div>
        <div className="text-xs text-gray-600 truncate mt-1">
          {valuePreview}
        </div>
      </div>

      {/* Drag indicator */}
      <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
        <Icons.GripVertical size={14} className="text-gray-400" />
      </div>
    </div>
  );
};

export default DraggableDataItem;
