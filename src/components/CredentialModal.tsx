import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as Icons from 'lucide-react';
import Modal from './Modal';
import { useCredentialsStore } from '../stores/nodes_store';
import type { CreateCredentialConfig, CredentialParameter } from '../stores/nodes_store';
import { useFieldVisibility } from '../hooks/useFieldVisibility';
import FieldRenderer from './shared/FieldRenderer';
import handleAxiosError from "../lib/axiosErrorHandle";
import { testCredentialById, getCredentialById, updateCredentialById, oauthCredential, editOAuthCredentialById } from '@/service/commonService';
import { toast } from 'react-toastify';
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(relativeTime);

interface CredentialModalProps {
  isOpen: boolean;
  onClose: () => void;
  credentialType: {
    name: string;
    display_name: string;
    description: string;
    icon: string;
    icon_color: string;
    parameters: CredentialParameter[];
    auth_methods?: string[];
  } | null;
  credentialId?: string| null;
  mode?: 'create' | 'edit';
}

interface CredentialData {
  [key: string]: {
    value: any;
    is_sensitive: boolean;
  };
}

interface CredentialResponse {
  id: string;
  name: string;
  type: string;
  auth_method: string;
  status: string;
  data: CredentialData;
  created_at: string;
  updated_at: string;
}

const CredentialModal: React.FC<CredentialModalProps> = ({
  isOpen,
  onClose,
  credentialType,
  credentialId,
  mode = 'create',
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingData, setIsFetchingData] = useState(false);
  const createCredential = useCredentialsStore((state) => state.createCredential);
  const [isTesting, setIsTesting] = useState(false);
  const [testResultMessage, setTestResultMessage] = useState("Couldn't connect with these settings");
  const [testStatus, setTestStatus] = useState<"success" | "error" | "idle">("idle");
  const [createdCredentialId, setCreatedCredentialId] = useState<string | null>(null);
  const [selectedAuthMethod, setSelectedAuthMethod] = useState<string|null>(null);
  const [originalFormValues, setOriginalFormValues] = useState<Record<string, any>>({});
  const [metadata, setMetadata] = useState<{
    created_at: string;
    updated_at: string;
  }>({
    created_at: "",
    updated_at: "",
  });

  // Determine if we're in edit mode
  const isEditMode = mode === 'edit' && credentialId;

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    reset,
    setValue,
  } = useForm();

  // Watch all form values for conditional display
  const formValues = watch();

  // Set auth_method in form values for visibility calculations
  const formValuesWithAuth = {
    ...formValues,
    auth_method: selectedAuthMethod
  };

  // Use shared visibility hook with auth method included
  const visibleFields = useFieldVisibility(credentialType?.parameters || [], formValuesWithAuth);

  // Helper function to get only changed fields
  const getChangedFields = (original: Record<string, any>, current: Record<string, any>) => {
    const changed: Record<string, any> = {};
    
    for (const key in current) {
      // Skip undefined or null values unless they were originally defined
      if (current[key] === undefined || current[key] === null) {
        if (original[key] !== undefined && original[key] !== null) {
          changed[key] = current[key]; // Include deletion/nullification
        }
        continue;
      }
      
      // Compare values (handle different types appropriately)
      if (original[key] !== current[key]) {
        changed[key] = current[key];
      }
    }
    
    return changed;
  };

  // Helper function to prepare parameters from form data
  const prepareParameters = (data: Record<string, any>, parameterKeys: string[]) => {
    const parameters: CreateCredentialConfig["parameters"] = {};

    parameterKeys.forEach((key) => {
      if (data[key] !== undefined) {
        const paramDef = credentialType?.parameters.find(p => p.name === key);
        const rawValue = data[key];
        
        if (paramDef && rawValue !== null && rawValue !== '') {
          // Convert the value based on the field type
          switch (paramDef.type) {
            case 'number':
              parameters[key] = Number(rawValue);
              break;
            case 'boolean':
              parameters[key] = Boolean(rawValue);
              break;
            case 'string':
            default:
              if (typeof rawValue === 'string') {
                try {
                  const parsed = JSON.parse(`"${rawValue}"`);
                  parameters[key] = parsed;
                } catch {
                  parameters[key] = rawValue;
                }
              } else {
                parameters[key] = String(rawValue);
              }
              break;
          }
        }
      }
    });

    return parameters;
  };

  // Helper function to build base payload
  const buildBasePayload = (data: Record<string, any>, displayName: string) => {
    return {
      name: displayName,
      type: credentialType!.name,
      auth_method: selectedAuthMethod,
      version: 1.0,
    };
  };

  // Fetch existing credential data when in edit mode
  useEffect(() => {
    if (!isOpen || !isEditMode || !credentialId) return;

    const fetchCredentialData = async () => {
      setIsFetchingData(true);
      try {
        const response: CredentialResponse = await getCredentialById(credentialId);
        
        // Set auth method
        setSelectedAuthMethod(response.auth_method || '');
        
        // Prepare form values
        const formValues: Record<string, any> = {
          display_name: response.name || '',
          auth_method: response.auth_method || '',
        };

        // Map API data structure to form fields
        Object.entries(response.data || {}).forEach(([key, fieldData]) => {
          formValues[key] = fieldData.value;
        });

        // Store original values for comparison
        setOriginalFormValues(formValues);

        // Reset form with fetched values
        reset(formValues);

        // Set metadata
        setMetadata({
          created_at: response.created_at || '',
          updated_at: response.updated_at || '',
        });

        // Set initial test status based on existing credential
        setTestStatus("idle");
        
      } catch (error) {
        handleAxiosError(error);
        toast.error("Failed to fetch credential data");
      } finally {
        setIsFetchingData(false);
      }
    };

    fetchCredentialData();
  }, [isOpen, isEditMode, credentialId, reset]);

  // Initialize selected auth method when modal opens (for create mode)
  useEffect(() => {
    if (isOpen && !isEditMode && credentialType?.auth_methods?.length) {
      const defaultAuthMethod = credentialType.auth_methods[0];
      setSelectedAuthMethod(defaultAuthMethod);
      setValue('auth_method', defaultAuthMethod);
    }
  }, [isOpen, isEditMode, credentialType, setValue]);

  const handleAuthMethodChange = (authMethod: string) => {
    setSelectedAuthMethod(authMethod);
    setValue('auth_method', authMethod);
  };

  const runCredentialTest = async (id: string) => {
    try {
      setIsTesting(true);
      const response = await testCredentialById(id);

      if (response.success === true) {
        setTestResultMessage("✅ Credential test passed");
        setTestStatus("success");
        toast.success("Credential test passed ✅");
      } else {
        const errorMessage = response?.error || "Test failed (no message)";
        setTestResultMessage(errorMessage);
        setTestStatus("error");
        toast.error(errorMessage);
      }
    } catch (err: any) {
      const errorMessage = err?.response?.data?.error || "❌ Unexpected test error";
      setTestResultMessage(errorMessage);
      setTestStatus("error");
      toast.error(errorMessage);
    } finally {
      setIsTesting(false);
    }
  };

  // Add a new function to handle OAuth button click
  const handleOAuthButtonClick = async (data: Record<string, any>) => {
    if (!credentialType) return;
    
    setIsLoading(true);
    try {
      const displayName = data.display_name || `${credentialType.display_name} Credential`;
      const parameterKeys = credentialType.parameters.map((param) => param.name);
      const parameters = prepareParameters(data, parameterKeys);
      
      if (isEditMode && credentialId) {
        // OAuth2 Edit Mode
        const updatePayload = {
          name: displayName,
          auth_method: selectedAuthMethod,
          parameters,
          redirect_uri: window.location.href,
          type: credentialType.name,
        };

        const authResponse = await editOAuthCredentialById(credentialId, updatePayload);
        if (authResponse.status === 'success' && authResponse.credential?.authorization_url) {
          window.open(authResponse.credential.authorization_url, '_blank');
          toast.success("Please complete re-authorization in the new tab");
          setOriginalFormValues({ ...originalFormValues, ...data });
        }
      } else {
        // OAuth2 Create Mode
        const payload = {
          ...buildBasePayload(data, displayName),
          parameters,
          redirect_uri: window.location.href,
        };

        const authResponse = await oauthCredential(payload);
        if (authResponse.status === 'success' && authResponse.credential?.authorization_url) {
          window.open(authResponse.credential.authorization_url, '_blank');
          if (authResponse.credential.credential_id) {
            setCreatedCredentialId(authResponse.credential.credential_id);
          }
          toast.success("Please complete authorization in the new tab");
        }
      }
    } catch (error) {
      handleAxiosError(error);
      toast.error(isEditMode ? "Failed to update OAuth2 credential" : "Failed to initiate OAuth2 authorization");
    } finally {
      setIsLoading(false);
    }
  };

  // Update onSubmit function to handle only non-OAuth flows
  const onSubmit = async (data: Record<string, string | number | boolean>) => {
    if (!credentialType) return;
    setIsLoading(true);

    try {
      const displayName = data.display_name || `${credentialType.display_name} Credential`;

      if (isEditMode && credentialId) {
        // Regular Edit Mode
        const changedData = getChangedFields(originalFormValues, data);
        
        if (Object.keys(changedData).length === 0) {
          toast.info("No changes detected");
          setIsLoading(false);
          return;
        }

        const changedParameterKeys = credentialType.parameters
          .map((param) => param.name)
          .filter(key => key in changedData);
        
        const parameters = prepareParameters(changedData, changedParameterKeys);
        const updatePayload = {
          name: displayName as string,
          auth_method: selectedAuthMethod,
          parameters,
          type: credentialType.name,
        };

        const credentialResult = await updateCredentialById(credentialId, updatePayload);
        if (credentialResult?.id) {
          await runCredentialTest(credentialResult.id);
          toast.success("Credential updated successfully");
          setOriginalFormValues({ ...originalFormValues, ...data });
        }
      } else {
        // Regular Create Mode
        const parameterKeys = credentialType.parameters.map((param) => param.name);
        const parameters = prepareParameters(data, parameterKeys);

        const credentialResult = await createCredential({
          name: displayName as string,
          type: credentialType.name,
          auth_method: selectedAuthMethod,
          parameters,
        });
        
        if (credentialResult?.id) {
          setCreatedCredentialId(credentialResult.id);
          await runCredentialTest(credentialResult.id);
          toast.success("Credential created successfully");
        } else {
          toast.error("Credential created but ID is missing.");
        }
      }
    } catch (error) {
      handleAxiosError(error);
      toast.error(isEditMode ? "Credential update failed" : "Credential creation failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setTestStatus("idle");
    setTestResultMessage("Couldn't connect with these settings");
    setCreatedCredentialId(null);
    setOriginalFormValues({}); // Clear original values
    setMetadata({ created_at: "", updated_at: "" });
    onClose();
  };

  const renderField = (param: CredentialParameter) => {
    if (!visibleFields.has(param.name)) return null;

    return (
      <FieldRenderer
        field={param}
        register={register}
        errors={errors}
        onSubmit={param.type === "oauth2_button" 
          ? handleSubmit(handleOAuthButtonClick)
          : undefined}
      />
    );
  };

  const getIcon = (iconName: string) => {
    const iconKey = iconName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');

    const IconsMap = Icons as unknown as Record<string, React.ComponentType<{ size?: number; className?: string }>>;
    return IconsMap[iconKey] || Icons.Circle;
  };

  if (!credentialType) return null;
  const IconComponent = getIcon(credentialType.icon);

  // Generate title based on mode
  const getModalTitle = () => {
    if (isEditMode) {
      return `Edit ${selectedAuthMethod || 'Credential'}`;
    }
    return credentialType.display_name;
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg" variant='credential'>
      <div className="flex items-center gap-4 mb-6">
        <div
          className="flex items-center justify-center w-12 h-12 rounded-full text-white"
          style={{ backgroundColor: credentialType.icon_color }}
        >
          <IconComponent size={20} className="text-white" />
        </div>
        <div>
          <h2 className="text-lg font-semibold text-black">
            {getModalTitle()}
          </h2>
          <p className="text-sm text-gray-400">{credentialType.description}</p>
        </div>
      </div>

      {/* Loading state for fetching data in edit mode */}
      {isFetchingData && (
        <div className="flex items-center justify-center py-8">
          <Icons.Loader2 className="w-6 h-6 animate-spin" />
          <span className="ml-2">Loading credential data...</span>
        </div>
      )}

      {!isFetchingData && (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            {/* Auth Method Tabs - Only show in CREATE mode */}
            {!isEditMode && credentialType.auth_methods && credentialType.auth_methods.length > 1 && (
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  {credentialType.auth_methods.map((authMethod) => (
                    <button
                      key={authMethod}
                      type="button"
                      onClick={() => handleAuthMethodChange(authMethod)}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        selectedAuthMethod === authMethod
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {authMethod}
                    </button>
                  ))}
                </nav>
              </div>
            )}

            {/* Test Status Messages */}
            {testStatus === "error" && (
              <div className="flex items-center justify-between bg-red-100 border border-red-300 text-red-800 text-sm px-4 py-3 rounded-md mb-4">
                <div className="flex items-center gap-2">
                  <Icons.AlertTriangle className="w-4 h-4" />
                  <span>{testResultMessage}</span>
                </div>
                <button
                  type="button"
                  disabled={isTesting}
                  className="text-sm font-medium text-white bg-red-500 hover:bg-red-600 px-3 py-1 rounded disabled:opacity-50"
                  onClick={async () => {
                    const testId = createdCredentialId || credentialId;
                    if (testId) {
                      await runCredentialTest(testId);
                    }
                  }}
                >
                  {isTesting ? "Testing..." : "Retry"}
                </button>
              </div>
            )}

            {testStatus === "success" && (
              <div className="flex items-center gap-2 bg-green-100 border border-green-300 text-green-800 text-sm px-4 py-3 rounded-md mb-4">
                <Icons.CheckCircle className="w-4 h-4" />
                <span>{testResultMessage}</span>
              </div>
            )}

            {/* Help Documentation */}
            <div className="flex items-center justify-between bg-yellow-100 border border-yellow-300 text-yellow-800 text-sm px-4 py-3 rounded-md mb-4">
              <span>Need help filling out these fields?</span>
              <a
                href="https://docs.example.com/credentials"
                target="_blank"
                rel="noopener noreferrer"
                className="text-yellow-900 underline font-medium hover:text-yellow-700"
              >
                Open docs
              </a>
            </div>

            {/* Display Name Field */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-500">
                Display Name
              </label>
              <input
                type="text"
                placeholder="Enter a display name (optional)"
                {...register('display_name')}
                className="w-full px-3 py-2 bg-white border border-gray-600 rounded-lg text-black placeholder-gray-500"
              />
            </div>

            {/* Dynamic Fields based on credential type parameters */}
            {credentialType.parameters.map((param) =>
              visibleFields.has(param.name) && (
                <div key={param.name}>
                  {renderField(param)}
                </div>
              )
            )}
          </div>

          {/* Metadata (only shown in edit mode) */}
          {isEditMode && (metadata.created_at || metadata.updated_at) && (
            <div className="border-t border-gray-200 mt-6 pt-4 text-xs text-gray-500 space-y-1">
              <div>
                <span className="font-medium">Created At:</span>{" "}
                {metadata.created_at ? dayjs(metadata.created_at).fromNow() : "N/A"}
              </div>
              <div>
                <span className="font-medium">Last updated:</span>{" "}
                {metadata.updated_at ? dayjs(metadata.updated_at).fromNow() : "N/A"}
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-300 bg-transparent border border-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 rounded-md hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading || isFetchingData}
            >
              {isLoading ? (
                <>
                  <Icons.Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                isEditMode ? 'Update Credential' : 'Create Credential'
              )}
            </button>
          </div>
        </form>
      )}
    </Modal>
  );
};

export default CredentialModal;
