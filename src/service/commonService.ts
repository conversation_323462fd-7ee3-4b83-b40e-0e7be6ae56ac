import { privateClient } from "@/utils/privateClient";
import type { NodeType } from "./nodeService";
import type { Workflow, WorkflowResponse } from "@/stores/workflow_store";
import type { CreateCredentialConfig } from "@/stores/nodes_store";
import handleAxiosError from "@/lib/axiosErrorHandle";
import { useCredentialsStore } from "@/stores/nodes_store";

export async function getNodeTypes(): Promise<NodeType[]> {
  try {
    const response = await privateClient.get("/nodes/");
    const { data, status } = response;
    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}

export async function getAllWorkflow(): Promise<WorkflowResponse> {
  try {
    const response = await privateClient.get("/workflows/");
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return {
        workflows: [],
        total: 0,
        page: 1,
        size: 100,
      } as WorkflowResponse; // Default empty response
    }
  } catch (error) {
    handleAxiosError(error);
    return { workflows: [], total: 0, page: 1, size: 100 } as WorkflowResponse; // Default empty response
  }
}
export async function getAddWorkflow(): Promise<Workflow | []> {
  try {
    const response = await privateClient.get("/workflows/new");
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}

export async function createWorkflow(
  body: Record<string, unknown>
): Promise<unknown> {
  try {
    const response = await privateClient.post("/workflows/", body);
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}

export async function getCredentials(): Promise<any[]> {
  try {
    const response = await privateClient.get("/credentials/");
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}

export async function createCredential(
  credentialData: CreateCredentialConfig
): Promise<any> {
  try {
    const response = await privateClient.post("/credentials/", credentialData);

    const { data, status } = response;

    if (status) {
      return {
        ...data.credential,
        parameters: data.parameters ?? credentialData.parameters,
      };
    } else {
      throw new Error("Failed to create credential");
    }
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

export async function getWorkflowById(
  workflowId: string
): Promise<Workflow | null> {
  try {
    const response = await privateClient.get(`/workflows/${workflowId}`);
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return null;
    }
  } catch (error) {
    handleAxiosError(error);
    return null;
  }
}

export async function executeWorkflow(
  workflowId: string,
  workflowData: {
    work_flow: {
      nodes: Record<string, any>;
      connections: Record<string, any>;
    };
    input_data: Record<string, any>;
  }
): Promise<any> {
  try {
    const response = await privateClient.post(
      `/workflows/execute/${workflowId}`,
      workflowData
    );
    const { data, status } = response;

    if (status || response.status === 200) {
      return data;
    } else {
      throw new Error("Failed to execute workflow");
    }
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

export async function executeSaveWorkflow(
  workflowId: string,
  workflowData: {
    work_flow: {
      nodes: Record<string, any>;
      connections: Record<string, any>;
    };
    input_data: Record<string, any>;
  }
): Promise<any> {
  try {
    const response = await privateClient.patch(
      `/workflows/${workflowId}`,
      workflowData
    );
    const { data, status } = response;

    if (status || response.status === 200) {
      return data;
    } else {
      throw new Error("Failed to execute workflow");
    }
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

export async function getSavedCredentials(
  credentialType: string
): Promise<any[]> {
  try {
    const response = await privateClient.get(`/credentials/type/${credentialType}`);
    const { data, status } = response;

    if (status) {
      // Save to store with credentialType as key
      const store = useCredentialsStore.getState();
      store.setSavedCredentialsByType?.(credentialType, data);
      
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}
/// api for get credential id

export async function getCredentialById(credentialId: string): Promise<any> {
  try {
    const response = await privateClient.get(`/credentials/${credentialId}`);
    const { data, status } = response;

    if (status === 200) {
      return data;
    } else {
      throw new Error("Failed to fetch credential");
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}
//test credential

export async function testCredentialById(credentialId: string): Promise<any> {
  try {
    const response = await privateClient.get(
      `/credentials/test/${credentialId}`
    );
    return response.data;
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

// update credential
export async function updateCredentialById(
  credentialId: string,
  credentialData: CreateCredentialConfig
): Promise<any> {
  try {
    const response = await privateClient.patch(
      `/credentials/${credentialId}`,
      credentialData
    );
    return response.data;
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

//Oauth credential
export async function oauthCredential(
  credentialData: CreateCredentialConfig
): Promise<any> {
  try {
    const response = await privateClient.post(
      `/credentials/authorize`,
      credentialData
    );
    return response.data;
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

//Update Oauth credential
export async function editOAuthCredentialById(
  credentialId: string,
  credentialData: CreateCredentialConfig
): Promise<any> {
  try {
    const response = await privateClient.patch(
      `/credentials/authorize/${credentialId}`,
      credentialData
    );
    return response.data;
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

export async function getNodeOptions(payload: {
      node_request: {
        name: string;
        type: string;
        display_properties: Record<string, unknown>;
        is_active: boolean;
        is_trigger: boolean;
        parameters: Record<string, unknown>;
        credentials: Record<string, { id: string; name: string }>;
      };
      function: string;
    }): Promise<any[]> {
      try {
        const response = await privateClient.post("/nodes/options", payload);
        const { data, status } = response;

        if (status) {
          return data;
        } else {
          return [];
        }
      } catch (error) {
        handleAxiosError(error);
        return [];
      }
    }
