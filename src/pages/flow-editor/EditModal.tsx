import React, { useEffect, useState } from "react";
import * as Icons from "lucide-react";
import Modal from "@/components/Modal";
import { useForm } from "react-hook-form";
import handleAxiosError from "@/lib/axiosErrorHandle";
import { getCredentialById, testCredentialById } from "@/service/commonService";
import DeleteCredential from "./DeleteCredential";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { toast } from "react-toastify";
dayjs.extend(relativeTime);

interface EditModalProps {
    isOpen: boolean;
    onClose: () => void;
    credentialType: {
        name: string;
        display_name: string;
        description: string;
        icon: string;
        icon_color: string;
    };
    credentialId: string;
}

const EditModal: React.FC<EditModalProps> = ({
    isOpen,
    onClose,
    credentialType,
    credentialId,
}) => {

    const [isDeleteOpen, setIsDeleteOpen] = useState(false);
    const [isTokenSensitive, setIsTokenSensitive] = useState(false);
    const [isTokenMasked, setIsTokenMasked] = useState(false);
    const [hasTypedToken, setHasTypedToken] = useState(false);


    const [isTesting, setIsTesting] = useState(false);
    const [testResultMessage, setTestResultMessage] = useState("Couldn't connect with these settings");
    const [testStatus, setTestStatus] = useState<"success" | "error" | "idle">("error");


    const [metadata, setMetadata] = useState<{

        created_at: string;
        updated_at: string;
    }>({});

    const { register, reset, formState: { errors } } = useForm({
        defaultValues: {
            name: "",
            access_token: "",
            business_id: "",
        },
    });

    useEffect(() => {
        if (!isOpen || !credentialId) return;

        const fetchCredential = async () => {
            try {
                const res = await getCredentialById(credentialId);
                const data = res.data || {};

                const sensitive = data?.access_token?.is_sensitive || false;
                setIsTokenSensitive(sensitive);
                setIsTokenMasked(sensitive);
                setHasTypedToken(false);

                reset({
                    name: res.name || "",
                    access_token: data?.access_token?.value || "",
                    business_id: data?.business_id?.value || "",
                });

                setMetadata({
                    created_at: res.created_at,
                    updated_at: res.updated_at,
                });
            } catch (err) {
                handleAxiosError(err);
            }
        };

        fetchCredential();

    }, [credentialId, isOpen, reset]);



    const getIcon = (iconName?: string) => {
        if (!iconName || typeof iconName !== "string") return Icons.Circle;

        const iconKey = iconName
            .split("-")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join("");

        const IconsMap = Icons as unknown as Record<
            string,
            React.ComponentType<{ size?: number; className?: string }>
        >;
        return IconsMap[iconKey] || Icons.Circle;
    };

    const IconComponent = getIcon(credentialType?.icon);

    return (
        <Modal isOpen={isOpen} onClose={onClose} size="lg" variant="credential">
            <div className="relative">
                <button
                    onClick={() => setIsDeleteOpen(true)}
                    className="absolute right-0 top-0 p-2 text-gray-400 hover:text-red-500"
                >
                    <Icons.Trash2 size={20} />
                </button>

                <DeleteCredential isOpen={isDeleteOpen} onClose={() => setIsDeleteOpen(false)} />

                <div className="flex items-center gap-4 mb-6">
                    <div
                        className="flex items-center justify-center w-12 h-12 rounded-full text-white"
                        style={{ backgroundColor: credentialType?.icon_color || "#000" }}
                    >
                        <IconComponent size={20} className="text-white" />
                    </div>
                    <div>
                        <h2 className="text-lg font-semibold text-black">
                            {credentialType?.display_name || "Edit Credential"}
                        </h2>
                        <p className="text-sm text-black">{credentialType?.description}</p>
                    </div>
                </div>

                <form className="space-y-6">
                    <div className="space-y-4">
                        {testStatus === "error" && (
                            <div className="flex items-center justify-between bg-red-100 border border-red-300 text-red-800 text-sm px-4 py-3 rounded-md mb-4">
                                <div className="flex items-center gap-2">
                                    <Icons.AlertTriangle className="w-4 h-4" />
                                    <span>{testResultMessage}</span>
                                </div>
                                <button
                                    type="button"
                                    disabled={isTesting}
                                    className="text-sm font-medium text-white bg-red-500 hover:bg-red-600 px-3 py-1 rounded disabled:opacity-50"
                                    onClick={async () => {
                                        try {
                                            setIsTesting(true);
                                            const response = await testCredentialById(credentialId);

                                            if (response.success === true) {
                                                setTestResultMessage("✅ Credential test passed");
                                                setTestStatus("success");
                                                toast.success("Credential test passed ✅");
                                            } else {
                                                const errorMessage = response?.error || "Test failed (no message)";
                                                setTestResultMessage(errorMessage);
                                                setTestStatus("error");
                                                toast.error(errorMessage);
                                            }
                                        } catch (err: any) {
                                            const errorMessage =
                                                err?.response?.data?.error || "❌ Unexpected test error";
                                            setTestResultMessage(errorMessage);
                                            setTestStatus("error");
                                            toast.error(errorMessage);
                                        } finally {
                                            setIsTesting(false);
                                        }
                                    }}

                                >
                                    {isTesting ? "Testing..." : "Retry"}
                                </button>
                            </div>
                        )}

                        {testStatus === "success" && (
                            <div className="flex items-center gap-2 bg-green-100 border border-green-300 text-green-800 text-sm px-4 py-3 rounded-md mb-4">
                                <Icons.CheckCircle className="w-4 h-4" />
                                <span>{testResultMessage}</span>
                            </div>
                        )}
                        <div className="flex items-center justify-between bg-yellow-100 border border-yellow-300 text-yellow-800 text-sm px-4 py-3 rounded-md mb-4">
                            <span>Need help filling out these fields?</span>
                            <a
                                href="https://docs.example.com/whatsapp-credentials"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-yellow-900 underline font-medium hover:text-yellow-700"
                            >
                                Open docs
                            </a>
                        </div>
                        {/*Display Name*/}
                        <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-500">
                                Name
                            </label>
                            <input
                                type="text"
                                placeholder="Enter a display name (optional)"
                                {...register('name')}
                                className="w-full px-3 py-2 bg-white border border-gray-600 rounded-lg text-black placeholder-gray-500"
                            />
                        </div>
                        {/* Access Token */}
                        <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-600">
                                Access Token <span className="text-red-500">*</span>
                            </label>
                            <div className="relative">
                                <input
                                    type={isTokenMasked ? "password" : "text"}
                                    {...register("access_token", {
                                        required: "Access Token is required",
                                        onChange: (e) => {
                                            // Only clear if first time typing AND token was prefilled
                                            if (!hasTypedToken && e.target.value.includes("***SENSITIVE_VALUE_HIDDEN***")) {
                                                e.target.value = "";
                                            }
                                            setHasTypedToken(true);
                                        },
                                    })}
                                    className={`w-full px-3 py-2 bg-white border ${errors.access_token ? "border-red-500" : "border-gray-600"
                                        } rounded-lg text-black placeholder-gray-500`}
                                    placeholder="Enter Access Token"
                                />
                                {isTokenSensitive && (
                                    <button
                                        type="button"
                                        onClick={() => setIsTokenMasked(!isTokenMasked)}
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                                    >
                                        {isTokenMasked ? <Icons.EyeOff size={18} /> : <Icons.Eye size={18} />}
                                    </button>
                                )}
                            </div>
                            {errors.access_token && (
                                <p className="text-sm text-red-500">{errors.access_token.message as string}</p>
                            )}
                        </div>
                        {/* Business ID */}
                        <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-600">
                                Business Id <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                {...register("business_id", {
                                    required: "Business Id is required",
                                })}
                                className={`w-full px-3 py-2 bg-white border ${errors.business_id ? "border-red-500" : "border-gray-600"
                                    } rounded-lg text-black placeholder-gray-500`}
                                placeholder="Enter Business Id"
                            />
                            {errors.business_id && (
                                <p className="text-sm text-red-500">{errors.business_id.message as string}</p>
                            )}
                        </div>
                    </div>
                    {/* Metadata */}
                    <div className="border-t border-gray-200 mt-6 pt-4 text-xs text-gray-500 space-y-1">
                        <div>
                            <span className="font-medium">Created At:</span>{" "}
                            {metadata.created_at ? dayjs(metadata.created_at).fromNow() : "N/A"}
                        </div>
                        <div>
                            <span className="font-medium">Last updated:</span>{" "}
                            {metadata.updated_at ? dayjs(metadata.updated_at).fromNow() : "N/A"}
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="flex justify-end space-x-3 pt-4">
                        <button
                            type="button"
                            onClick={onClose}
                            className="px-4 py-2 text-sm font-medium text-gray-300 bg-transparent border border-gray-600 rounded-md hover:bg-gray-700"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 rounded-md hover:from-blue-600 hover:to-purple-700"
                        >
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </Modal>
    );
};

export default EditModal;
